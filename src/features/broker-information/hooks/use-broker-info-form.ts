"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { brokerInfoSchema, BrokerInfoFormData } from "../lib/validation";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { toast } from "@/shared/lib/toast";
import { formatUrl } from "@/shared/lib/format-url";

export function useBrokerInfoForm() {
  const { userAuth } = useAuthContext();
  const { forms, updateForm, saveForm } = useFormsContext();
  const [isLoading, setIsLoading] = React.useState(false);
  const [charCount, setCharCount] = React.useState({ bio: 0, additionalNotes: 0 });
  const [sameAddressWarning, setSameAddressWarning] = React.useState({
    showMessage: false,
    sameAddress: null as boolean | null,
  });
  const [branches, setBranches] = React.useState<any[]>([]);
  const [showOtherMortgageSoftware, setShowOtherMortgageSoftware] = React.useState(false);
  const [processingStatus, setProcessingStatus] = React.useState({
    visible: false,
    status: '',
    message: ''
  });
  const [formStatus, setFormStatus] = React.useState({
    isComplete: false,
    completionPercentage: 0
  });
  const [validationErrors, setValidationErrors] = React.useState<{
    fieldErrors: Record<string, string[]>;
    crossFieldErrors: string[];
    requiredFields: string[];
  }>({
    fieldErrors: {},
    crossFieldErrors: [],
    requiredFields: []
  });

  const form = useForm<BrokerInfoFormData>({
    resolver: zodResolver(brokerInfoSchema) as any,
    defaultValues: {
      // Personal Information
      firstName: "",
      middleName: "",
      lastName: "",
      legalName: "",
      preferredName: "",
      titles: "",
      position: "",
      license: "",
      birthdate: "",
      sin: "",
      tshirtSize: "M",
      bio: "",
      additionalNotes: "",

      // Contact Information
      workEmail: "",
      workPhone: "",
      ext: "",
      homePhone: "",
      cellPhone: "",
      emergencyContact: "",
      emergencyPhone: "",

      // Office Address
      address: "",
      suiteUnit: "",
      city: "",
      province: "",
      postalCode: "",
      brokerageLicense: "",

      // Personal Address
      personalAddress: "",
      personalSuiteUnit: "",
      personalCity: "",
      personalProvince: "",
      personalPostalCode: "",
      sameAddress: false,

      // Brokering Background
      existingAgent: undefined,
      mortgageSoftware: "",
      otherMortgageSoftware: "",
      lender1: "",
      lender1Volume: "",
      lender2: "",
      lender2Volume: "",
      lender3: "",
      lender3Volume: "",

      // Enhanced Social Media
      hasFacebook: false,
      facebook: "",
      facebookHandler: "",
      hasInstagram: false,
      instagram: "",
      instagramHandler: "",
      hasLinkedin: false,
      linkedin: "",
      linkedinHandler: "",
      hasYoutube: false,
      youtube: "",
      youtubeHandler: "",
      hasTwitter: false,
      twitter: "",
      twitterHandler: "",
      hasTikTok: false,
      tiktok: "",
      tiktokHandler: "",
      hasPinterest: false,
      pinterest: "",
      pinterestHandler: "",
      hasThreads: false,
      threads: "",
      threadsHandler: "",
      hasBluesky: false,
      bluesky: "",
      blueskyHandler: "",

      // Declarations
      declarationRegulatoryReview: undefined,
      declarationClientComplaints: undefined,
      declarationRegulatoryReviewDetails: "",
      declarationClientComplaintsDetails: "",

      // Signature
      signature: "",

      // Form metadata
      isFormComplete: false,
      firstSaveComplete: false,
    },
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Load existing form data
  React.useEffect(() => {
    if (forms.brokerInfo) {
      const formData = forms.brokerInfo;

      // Define the form fields that should be loaded
      const formFields: (keyof BrokerInfoFormData)[] = [
        'firstName', 'middleName', 'lastName', 'legalName', 'preferredName', 'titles', 'position',
        'license', 'birthdate', 'sin', 'tshirtSize', 'bio', 'additionalNotes',
        'workEmail', 'workPhone', 'ext', 'homePhone', 'cellPhone', 'emergencyContact', 'emergencyPhone',
        'address', 'suiteUnit', 'city', 'province', 'postalCode', 'brokerageLicense',
        'personalAddress', 'personalSuiteUnit', 'personalCity', 'personalProvince',
        'personalPostalCode', 'sameAddress',
        'existingAgent', 'mortgageSoftware', 'otherMortgageSoftware',
        'lender1', 'lender1Volume', 'lender2', 'lender2Volume', 'lender3', 'lender3Volume',
        'hasFacebook', 'facebook', 'facebookHandler', 'hasInstagram', 'instagram', 'instagramHandler',
        'hasLinkedin', 'linkedin', 'linkedinHandler', 'hasYoutube', 'youtube', 'youtubeHandler',
        'hasTwitter', 'twitter', 'twitterHandler', 'hasTikTok', 'tiktok', 'tiktokHandler',
        'hasPinterest', 'pinterest', 'pinterestHandler', 'hasThreads', 'threads', 'threadsHandler',
        'hasBluesky', 'bluesky', 'blueskyHandler',
        'declarationRegulatoryReview', 'declarationClientComplaints',
        'declarationRegulatoryReviewDetails', 'declarationClientComplaintsDetails', 'signature',
        'isFormComplete', 'firstSaveComplete'
      ];

      formFields.forEach((field) => {
        if (formData[field as keyof typeof formData] !== undefined && formData[field as keyof typeof formData] !== null) {
          setValue(field, formData[field as keyof typeof formData] as any);
        }
      });

      // Set character counts
      if (formData.bio) {
        setCharCount(prev => ({ ...prev, bio: formData.bio?.length || 0 }));
      }
      if (formData.additionalNotes) {
        setCharCount(prev => ({ ...prev, additionalNotes: formData.additionalNotes?.length || 0 }));
      }

      // Set mortgage software conditional state
      if (formData.mortgageSoftware === "Other") {
        setShowOtherMortgageSoftware(true);
      }

      // Update form status
      setFormStatus({
        isComplete: formData.isFormComplete || false,
        completionPercentage: calculateCompletionPercentage(formData)
      });
    }
  }, [forms.brokerInfo, setValue]);

  // Calculate form completion percentage
  const calculateCompletionPercentage = (data: any): number => {
    const requiredFields = [
      'firstName', 'lastName', 'position', 'workEmail', 'address', 'city', 'province', 'postalCode',
      'personalAddress', 'personalCity', 'personalProvince', 'personalPostalCode',
      'workPhone', 'birthdate', 'sin', 'emergencyContact', 'emergencyPhone', 'bio'
    ];

    // Add Saskatchewan-specific field
    if (data.province === "Saskatchewan") {
      requiredFields.push('brokerageLicense');
    }

    // Add declaration fields for licensed users
    if (isLicensed) {
      requiredFields.push('declarationRegulatoryReview', 'declarationClientComplaints');
      if (data.declarationRegulatoryReview === true) {
        requiredFields.push('declarationRegulatoryReviewDetails');
      }
      if (data.declarationClientComplaints === true) {
        requiredFields.push('declarationClientComplaintsDetails');
      }
    }

    const completedFields = requiredFields.filter(field => {
      const value = data[field];
      return value !== undefined && value !== null && value !== '';
    });

    return Math.round((completedFields.length / requiredFields.length) * 100);
  };

  // Validate form and update validation errors
  const validateForm = (data: any) => {
    const fieldErrors: Record<string, string[]> = {};
    const crossFieldErrors: string[] = [];
    const requiredFields: string[] = [];

    // Required field validation
    const requiredFieldsList = [
      { field: 'firstName', label: 'First Name' },
      { field: 'lastName', label: 'Last Name' },
      { field: 'position', label: 'Position' },
      { field: 'workEmail', label: 'Work Email' },
      { field: 'address', label: 'Office Address' },
      { field: 'city', label: 'City' },
      { field: 'province', label: 'Province' },
      { field: 'postalCode', label: 'Postal Code' },
      { field: 'personalAddress', label: 'Personal Address' },
      { field: 'personalCity', label: 'Personal City' },
      { field: 'personalProvince', label: 'Personal Province' },
      { field: 'personalPostalCode', label: 'Personal Postal Code' },
      { field: 'workPhone', label: 'Work Phone' },
      { field: 'birthdate', label: 'Birth Date' },
      { field: 'sin', label: 'SIN' },
      { field: 'emergencyContact', label: 'Emergency Contact' },
      { field: 'emergencyPhone', label: 'Emergency Phone' },
      { field: 'bio', label: 'Bio' }
    ];

    // Add Saskatchewan-specific field
    if (data.province === "Saskatchewan") {
      requiredFieldsList.push({ field: 'brokerageLicense', label: 'Brokerage License' });
    }

    // Add declaration fields for licensed users
    if (isLicensed) {
      requiredFieldsList.push(
        { field: 'declarationRegulatoryReview', label: 'Regulatory Review Declaration' },
        { field: 'declarationClientComplaints', label: 'Client Complaints Declaration' }
      );

      if (data.declarationRegulatoryReview === true) {
        requiredFieldsList.push({ field: 'declarationRegulatoryReviewDetails', label: 'Regulatory Review Details' });
      }
      if (data.declarationClientComplaints === true) {
        requiredFieldsList.push({ field: 'declarationClientComplaintsDetails', label: 'Client Complaints Details' });
      }
    }

    // Check required fields
    requiredFieldsList.forEach(({ field, label }) => {
      const value = data[field];
      if (value === undefined || value === null || value === '') {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push(`${label} is required`);
        requiredFields.push(label);
      }
    });

    // Cross-field validation
    if (data.mortgageSoftware === "Other" && (!data.otherMortgageSoftware || data.otherMortgageSoftware.trim() === '')) {
      crossFieldErrors.push('Please specify the other mortgage software when "Other" is selected');
    }

    // Email validation
    if (data.workEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.workEmail)) {
      if (!fieldErrors.workEmail) fieldErrors.workEmail = [];
      fieldErrors.workEmail.push('Please enter a valid email address');
    }

    // Phone validation
    const phoneFields = ['workPhone', 'homePhone', 'cellPhone', 'emergencyPhone'];
    phoneFields.forEach(field => {
      const value = data[field];
      if (value && !/^\(\d{3}\) \d{3}-\d{4}$/.test(value)) {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push('Please enter a valid phone number format: (*************');
      }
    });

    // SIN validation
    if (data.sin && !/^\d{3}-\d{3}-\d{3}$/.test(data.sin)) {
      if (!fieldErrors.sin) fieldErrors.sin = [];
      fieldErrors.sin.push('Please enter a valid SIN format: 123-456-789');
    }

    // Postal code validation
    const postalFields = ['postalCode', 'personalPostalCode'];
    postalFields.forEach(field => {
      const value = data[field];
      if (value && !/^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/.test(value)) {
        if (!fieldErrors[field]) fieldErrors[field] = [];
        fieldErrors[field].push('Please enter a valid postal code format: A1A 1A1');
      }
    });

    setValidationErrors({ fieldErrors, crossFieldErrors, requiredFields });
    return Object.keys(fieldErrors).length === 0 && crossFieldErrors.length === 0;
  };

  // Fetch branches data
  React.useEffect(() => {
    const fetchBranches = async () => {
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1339/api';
        const response = await fetch(`${apiUrl}/branches?populate=*`);
        if (response.ok) {
          const data = await response.json();
          setBranches(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
      }
    };

    fetchBranches();
  }, []);

  // Handle form submission
  const onSubmit = async (data: BrokerInfoFormData) => {
    setIsLoading(true);
    setProcessingStatus({ visible: true, status: 'loading', message: 'Updating Broker Information...' });

    try {
      // Update form data in context
      updateForm('brokerInfo', {
        ...data,
        isFormComplete: true,
        firstSaveComplete: true,
        lastUpdated: new Date().toISOString(),
      });

      setProcessingStatus({ visible: true, status: 'loading', message: 'Saving form...' });

      // Save to backend
      await saveForm('brokerInfo');

      setProcessingStatus({ visible: true, status: 'success', message: 'Broker Information form saved!' });
      toast.success("Broker information saved successfully!");

      // Hide success message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);

    } catch (error) {
      console.error("Error saving broker information:", error);
      setProcessingStatus({ visible: true, status: 'error', message: 'Failed to save broker information' });
      toast.error("Failed to save broker information");

      // Hide error message after delay
      setTimeout(() => {
        setProcessingStatus({ visible: false, status: '', message: '' });
      }, 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle bio character count
  const handleBioChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, bio: value.length }));
    setValue('bio', value);
  };

  // Handle additional notes character count
  const handleAdditionalNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCharCount(prev => ({ ...prev, additionalNotes: value.length }));
    setValue('additionalNotes', value);
  };

  // Handle mortgage software change
  const handleMortgageSoftwareChange = (value: string) => {
    setValue('mortgageSoftware', value);
    setShowOtherMortgageSoftware(value === "Other");
    if (value !== "Other") {
      setValue('otherMortgageSoftware', "");
    }
  };

  // Handle social media URL formatting
  const handleSocialMediaUrlChange = (field: keyof BrokerInfoFormData, value: string) => {
    const formattedUrl = formatUrl(value);
    setValue(field, formattedUrl);
  };

  // Handle social media toggle
  const handleSocialMediaToggle = (platform: string, checked: boolean) => {
    const toggleField = `has${platform}` as keyof BrokerInfoFormData;
    const urlField = platform.toLowerCase() as keyof BrokerInfoFormData;
    const handlerField = `${platform.toLowerCase()}Handler` as keyof BrokerInfoFormData;

    setValue(toggleField, checked);
    if (!checked) {
      setValue(urlField, "");
      setValue(handlerField, "");
    }
  };

  // Handle phone number formatting
  const handlePhoneChange = (field: string, value: string) => {
    const formattedPhone = formatPhoneNumber(value);
    setValue(field as keyof BrokerInfoFormData, formattedPhone);
  };

  // Format phone number helper
  const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  // Handle currency value formatting
  const handleCurrencyChange = (field: string, value: number | string) => {
    setValue(field as keyof BrokerInfoFormData, value.toString());
  };

  // Handle same address toggle
  const handleSameAddressChange = (checked: boolean) => {
    setValue('sameAddress', checked);
    if (checked) {
      setValue('personalAddress', watchedValues.address);
      setValue('personalSuiteUnit', watchedValues.suiteUnit);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Enhanced same address warning system
  React.useEffect(() => {
    if (!watchedValues.sameAddress) {
      const officeFields = ['address', 'suiteUnit', 'city', 'province', 'postalCode'];
      const personalFields = ['personalAddress', 'personalSuiteUnit', 'personalCity', 'personalProvince', 'personalPostalCode'];

      let hasMatchingFields = false;
      for (let i = 0; i < officeFields.length; i++) {
        const officeValue = watchedValues[officeFields[i] as keyof BrokerInfoFormData] as string;
        const personalValue = watchedValues[personalFields[i] as keyof BrokerInfoFormData] as string;

        if (officeValue && personalValue &&
            officeValue.replace(/\s/g, '').toLowerCase() === personalValue.replace(/\s/g, '').toLowerCase()) {
          hasMatchingFields = true;
          break;
        }
      }

      if (hasMatchingFields && sameAddressWarning.sameAddress === null) {
        setSameAddressWarning({ showMessage: true, sameAddress: false });
      }
    }
  }, [watchedValues.address, watchedValues.suiteUnit, watchedValues.city, watchedValues.province, watchedValues.postalCode,
      watchedValues.personalAddress, watchedValues.personalSuiteUnit, watchedValues.personalCity,
      watchedValues.personalProvince, watchedValues.personalPostalCode, watchedValues.sameAddress, sameAddressWarning.sameAddress]);

  // Handle same address warning response
  const handleSameAddressWarningResponse = (response: boolean) => {
    setSameAddressWarning({ showMessage: false, sameAddress: response });
    if (response) {
      setValue('sameAddress', true);
      setValue('personalAddress', watchedValues.address);
      setValue('personalSuiteUnit', watchedValues.suiteUnit);
      setValue('personalCity', watchedValues.city);
      setValue('personalProvince', watchedValues.province);
      setValue('personalPostalCode', watchedValues.postalCode);
    }
  };

  // Handle branch selection
  const handleBranchSelect = (addressInfo: any) => {
    setValue('address', addressInfo.address);
    setValue('suiteUnit', addressInfo.suiteUnit || "");
    setValue('city', addressInfo.city);
    setValue('province', addressInfo.province);
    setValue('postalCode', addressInfo.postalCode);
    if (addressInfo.brokerageLicense) {
      setValue('brokerageLicense', addressInfo.brokerageLicense);
    }
  };

  // Check if user is licensed (affects which fields to show)
  const isLicensed = userAuth.userInfo?.license !== undefined && userAuth.userInfo?.license !== null;

  return {
    form,
    watchedValues,
    errors,
    isLoading,
    charCount,
    sameAddressWarning,
    branches,
    isLicensed,
    showOtherMortgageSoftware,
    processingStatus,
    formStatus,
    validationErrors,
    validateForm,
    handleBioChange,
    handleAdditionalNotesChange,
    handleMortgageSoftwareChange,
    handleSocialMediaUrlChange,
    handleSocialMediaToggle,
    handlePhoneChange,
    handleCurrencyChange,
    handleSameAddressChange,
    handleSameAddressWarningResponse,
    handleBranchSelect,
    handleSubmit: handleSubmit(onSubmit),
  };
}
