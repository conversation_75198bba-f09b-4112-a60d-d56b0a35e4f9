"use client";

import * as React from "react";
import { User, Phone, MapPin, FileText, Scale, Briefcase } from "lucide-react";
import { Card, CardContent } from "@/shared/ui/card";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Textarea } from "@/shared/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select";
import { FormSectionTitle } from "@/shared/ui/form-section-title";
import { PhoneInput, SinInput } from "@/shared/ui/masked-inputs";
import { AddressSelect } from "@/shared/ui/address-select";
import { SwitcherBox } from "@/shared/ui/switcher-box";
import { CurrencyInputField } from "@/shared/ui/currency-input";
import { SignatureSection } from "@/shared/components/signature-section";
import { NextPrevFooter } from "@/shared/ui/next-prev-footer";
import { useBrokerInfoForm } from "../hooks/use-broker-info-form";

export function BrokerInformationForm() {
  const {
    form,
    watchedValues,
    errors,
    isLoading,
    charCount,
    sameAddressWarning,
    branches,
    isLicensed,
    showOtherMortgageSoftware,
    processingStatus,
    formStatus,
    validationErrors,
    validateForm,
    handleBioChange,
    handleAdditionalNotesChange,
    handleMortgageSoftwareChange,
    handleSocialMediaUrlChange,
    handleSocialMediaToggle,
    handlePhoneChange,
    handleCurrencyChange,
    handleSameAddressChange,
    handleSameAddressWarningResponse,
    handleBranchSelect,
    handleSubmit,
  } = useBrokerInfoForm();

  const { setValue } = form;

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Form Header with Status */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Broker Information</h1>
          <h2 className={`text-lg font-medium ${formStatus.isComplete ? 'text-green-600' : 'text-red-600'}`}>
            Status: <span className="uppercase">{formStatus.isComplete ? 'Complete' : 'Incomplete'}</span>
            {!formStatus.isComplete && (
              <span className="text-sm text-gray-500 ml-2">
                ({formStatus.completionPercentage}% complete)
              </span>
            )}
          </h2>
        </div>
      </div>

      {/* Processing Status Overlay */}
      {processingStatus.visible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full mx-4">
            <div className="flex items-center space-x-3">
              {processingStatus.status === 'loading' && (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              )}
              {processingStatus.status === 'success' && (
                <div className="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
              {processingStatus.status === 'error' && (
                <div className="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
              )}
              <p className="text-sm font-medium">{processingStatus.message}</p>
            </div>
          </div>
        </div>
      )}

      {/* Validation Errors Display */}
      {(validationErrors.crossFieldErrors.length > 0 || validationErrors.requiredFields.length > 0) && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            Please fix the following errors:
          </h3>

          {validationErrors.crossFieldErrors.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-medium text-red-700 mb-1">Form Errors:</h4>
              <ul className="list-disc list-inside text-sm text-red-600">
                {validationErrors.crossFieldErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {validationErrors.requiredFields.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-red-700 mb-1">
                Missing Required Fields ({validationErrors.requiredFields.length}):
              </h4>
              <ul className="list-disc list-inside text-sm text-red-600">
                {validationErrors.requiredFields.map((field, index) => (
                  <li key={index}>{field}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information Section */}
            <div>
              <FormSectionTitle
                title="Personal Information"
                icon={<User className="h-5 w-5" />}
                description="Please provide your personal details"
              />
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="firstName">
                    First Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    {...form.register("firstName")}
                    placeholder="First Name"
                    className={errors.firstName ? "border-red-500" : ""}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="middleName">Middle Name</Label>
                  <Input
                    id="middleName"
                    {...form.register("middleName")}
                    placeholder="Middle Name"
                  />
                </div>

                <div>
                  <Label htmlFor="lastName">
                    Last Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    {...form.register("lastName")}
                    placeholder="Last Name"
                    className={errors.lastName ? "border-red-500" : ""}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
                  )}
                </div>

                 <div>
                  <Label htmlFor="preferredName">Preferred Name</Label>
                  <Input
                    id="preferredName"
                    {...form.register("preferredName")}
                    placeholder="Preferred Name"
                  />
                </div>

                <div>
                  <Label htmlFor="titles">Titles After Name (e.g. AMP, BCC)</Label>
                  <Input
                    id="titles"
                    {...form.register("titles")}
                    placeholder="AMP, BCC, BCO"
                  />
                </div>

                <div>
                  <Label htmlFor="position">
                    Position <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="position"
                    {...form.register("position")}
                    placeholder="I.E: Mortgage Broker"
                    className={errors.position ? "border-red-500" : ""}
                  />
                  {errors.position && (
                    <p className="text-red-500 text-sm mt-1">{errors.position.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div>
                    <Label htmlFor="license">License Number</Label>
                    <Input
                      id="license"
                      {...form.register("license")}
                      placeholder="I.E: #AXM003333"
                    />
                  </div>

                  <div>
                    <Label htmlFor="birthdate">
                      Birthdate <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="birthdate"
                      type="date"
                      {...form.register("birthdate")}
                      className={errors.birthdate ? "border-red-500" : ""}
                    />
                    {errors.birthdate && (
                      <p className="text-red-500 text-sm mt-1">{errors.birthdate.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="sin">
                      SIN <span className="text-red-500">*</span>
                    </Label>
                    <SinInput
                      id="sin"
                      value={watchedValues.sin}
                      onChange={(e) => setValue('sin', e.target.value)}
                      className={errors.sin ? "border-red-500" : ""}
                    />
                    {errors.sin && (
                      <p className="text-red-500 text-sm mt-1">{errors.sin.message}</p>
                    )}
                  </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <Label htmlFor="tshirtSize">T-Shirt Size</Label>
                  <Select
                    value={watchedValues.tshirtSize}
                    onValueChange={(value) => setValue('tshirtSize', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="XS">XS</SelectItem>
                      <SelectItem value="S">S</SelectItem>
                      <SelectItem value="M">M</SelectItem>
                      <SelectItem value="L">L</SelectItem>
                      <SelectItem value="XL">XL</SelectItem>
                      <SelectItem value="XXL">XXL</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <Label htmlFor="bio">
                    More About Me (Bio) <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="bio"
                    value={watchedValues.bio}
                    onChange={handleBioChange}
                    placeholder="Tell us about yourself..."
                    className={`min-h-[100px] ${errors.bio ? "border-red-500" : ""}`}
                    maxLength={800}
                  />
                  <div className="flex justify-between items-center mt-1">
                    {errors.bio && (
                      <p className="text-red-500 text-sm">{errors.bio.message}</p>
                    )}
                    <p className="text-sm text-muted-foreground ml-auto">
                      {charCount.bio}/800 characters
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="additionalNotes">Additional Notes</Label>
                  <Textarea
                    id="additionalNotes"
                    value={watchedValues.additionalNotes}
                    onChange={handleAdditionalNotesChange}
                    placeholder="Any additional notes..."
                    className="min-h-[100px]"
                    maxLength={800}
                  />
                  <div className="flex justify-end mt-1">
                    <p className="text-sm text-muted-foreground">
                      {charCount.additionalNotes}/800 characters
                    </p>
                  </div>
                </div>
              </div>               
            </div>

              
                

              
              

            {/* Brokering Background Section */}
            <div>
              <FormSectionTitle
                title="Brokering Background"
                icon={<Briefcase className="h-5 w-5" />}
                description="Tell us about your mortgage brokering experience"
              />

              <div className="space-y-6">
                {/* Existing Agent Question */}
                <div>
                  <Label className="text-base font-medium">
                    Are you an existing agent? <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex gap-6 mt-2">
                    <SwitcherBox
                      id="existingAgentYes"
                      name="existingAgent"
                      checked={watchedValues.existingAgent === true}
                      onChange={(checked) => setValue('existingAgent', checked ? true : undefined)}
                      label="Yes"
                      yesno={false}
                    />
                    <SwitcherBox
                      id="existingAgentNo"
                      name="existingAgent"
                      checked={watchedValues.existingAgent === false}
                      onChange={(checked) => setValue('existingAgent', checked ? false : undefined)}
                      label="No"
                      yesno={false}
                    />
                  </div>
                </div>

                {/* Mortgage Software Platform */}
                <div>
                  <Label htmlFor="mortgageSoftware">Mortgage Software Platform</Label>
                  <Select
                    value={watchedValues.mortgageSoftware}
                    onValueChange={handleMortgageSoftwareChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select mortgage software" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Expert">Expert</SelectItem>
                      <SelectItem value="ExpertPro">ExpertPro</SelectItem>
                      <SelectItem value="Finmo">Finmo</SelectItem>
                      <SelectItem value="Scarlett">Scarlett</SelectItem>
                      <SelectItem value="Velocity">Velocity</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Other Mortgage Software - Conditional */}
                {showOtherMortgageSoftware && (
                  <div>
                    <Label htmlFor="otherMortgageSoftware">
                      Other Mortgage Software <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="otherMortgageSoftware"
                      {...form.register("otherMortgageSoftware")}
                      placeholder="Please specify"
                      className={errors.otherMortgageSoftware ? "border-red-500" : ""}
                    />
                    {errors.otherMortgageSoftware && (
                      <p className="text-red-500 text-sm mt-1">{errors.otherMortgageSoftware.message}</p>
                    )}
                  </div>
                )}

                {/* Top 3 Lenders */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Top 3 Lenders</h3>

                  {/* Lender 1 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <Label htmlFor="lender1">Lender 1 Name</Label>
                      <Input
                        id="lender1"
                        {...form.register("lender1")}
                        placeholder="Lender name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lender1Volume">Lender 1 Volume</Label>
                      <CurrencyInputField
                        id="lender1Volume"
                        value={parseFloat(watchedValues.lender1Volume || "0")}
                        onValueChange={(value) => handleCurrencyChange('lender1Volume', value || "")}
                        placeholder="$0.00"
                      />
                    </div>
                  </div>

                  {/* Lender 2 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <Label htmlFor="lender2">Lender 2 Name</Label>
                      <Input
                        id="lender2"
                        {...form.register("lender2")}
                        placeholder="Lender name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lender2Volume">Lender 2 Volume</Label>
                      <CurrencyInputField
                        id="lender2Volume"
                        value={parseFloat(watchedValues.lender2Volume || "0")}
                        onValueChange={(value) => handleCurrencyChange('lender2Volume', value || "")}
                        placeholder="$0.00"
                      />
                    </div>
                  </div>

                  {/* Lender 3 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="lender3">Lender 3 Name</Label>
                      <Input
                        id="lender3"
                        {...form.register("lender3")}
                        placeholder="Lender name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lender3Volume">Lender 3 Volume</Label>
                      <CurrencyInputField
                        id="lender3Volume"
                        value={parseFloat(watchedValues.lender3Volume || "0")}
                        onValueChange={(value) => handleCurrencyChange('lender3Volume', value || "")}
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Office Address Section */}
            <div>
              <FormSectionTitle
                title="Office Address"
                icon={<MapPin className="h-5 w-5" />}
                description="Your primary work address"
              />

              {/* Branch Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="mb-4">
                  <AddressSelect
                    branches={branches}
                    onAddressSelect={handleBranchSelect}
                    className="w-full"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="address">
                    Office Address (Street) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="address"
                    {...form.register("address")}
                    placeholder="123 Main Street"
                    className={errors.address ? "border-red-500" : ""}
                  />
                  {errors.address && (
                    <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="suiteUnit">Suite/Unit</Label>
                  <Input
                    id="suiteUnit"
                    {...form.register("suiteUnit")}
                    placeholder="Suite 100"
                  />
                </div>

                <div>
                  <Label htmlFor="city">
                    City <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="city"
                    {...form.register("city")}
                    placeholder="Calgary"
                    className={errors.city ? "border-red-500" : ""}
                  />
                  {errors.city && (
                    <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="province">
                    Province <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watchedValues.province}
                    onValueChange={(value) => setValue('province', value)}
                  >
                    <SelectTrigger className={errors.province ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select province" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Alberta">Alberta</SelectItem>
                      <SelectItem value="British Columbia">British Columbia</SelectItem>
                      <SelectItem value="Manitoba">Manitoba</SelectItem>
                      <SelectItem value="New Brunswick">New Brunswick</SelectItem>
                      <SelectItem value="Newfoundland And Labrador">Newfoundland and Labrador</SelectItem>
                      <SelectItem value="Nova Scotia">Nova Scotia</SelectItem>
                      <SelectItem value="Ontario">Ontario</SelectItem>
                      <SelectItem value="Prince Edward Island">Prince Edward Island</SelectItem>
                      <SelectItem value="Quebec">Quebec</SelectItem>
                      <SelectItem value="Saskatchewan">Saskatchewan</SelectItem>
                      <SelectItem value="Northwest Territories">Northwest Territories</SelectItem>
                      <SelectItem value="Nunavut">Nunavut</SelectItem>
                      <SelectItem value="Yukon">Yukon</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.province && (
                    <p className="text-red-500 text-sm mt-1">{errors.province.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="postalCode">
                    Postal Code <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="postalCode"
                    {...form.register("postalCode")}
                    placeholder="T2P 1J9"
                    className={errors.postalCode ? "border-red-500" : ""}
                  />
                  {errors.postalCode && (
                    <p className="text-red-500 text-sm mt-1">{errors.postalCode.message}</p>
                  )}
                </div>
              </div>

              {/* Saskatchewan Brokerage License */}
              {watchedValues.province === "Saskatchewan" && (
                <div className="mt-4">
                  <Label htmlFor="brokerageLicense">
                    Brokerage License Number <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="brokerageLicense"
                    {...form.register("brokerageLicense")}
                    placeholder="Brokerage License Number"
                    className={errors.brokerageLicense ? "border-red-500" : ""}
                  />
                  {errors.brokerageLicense && (
                    <p className="text-red-500 text-sm mt-1">{errors.brokerageLicense.message}</p>
                  )}
                </div>
              )}
            </div>

            {/* Personal Address Section */}
            <div>
              <FormSectionTitle
                title="Personal Address"
                icon={<MapPin className="h-5 w-5" />}
                description="Your home address"
              />

              <div className="mb-4">
                <SwitcherBox
                  id="sameAddress"
                  name="sameAddress"
                  checked={watchedValues.sameAddress}
                  onChange={handleSameAddressChange}
                  label="Same as office address"
                  yesno={true}
                />
              </div>

              {/* Same Address Warning */}
              {sameAddressWarning.showMessage && (
                <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h3 className="text-sm font-medium text-yellow-800 mb-2">
                    Address Similarity Detected
                  </h3>
                  <p className="text-sm text-yellow-700 mb-3">
                    Your personal address appears to be the same as your office address.
                    Would you like to mark them as the same?
                  </p>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={() => handleSameAddressWarningResponse(true)}
                    >
                      Yes, they're the same
                    </Button>
                    <Button
                      type="button"
                      size="sm"
                      variant="outline"
                      onClick={() => handleSameAddressWarningResponse(false)}
                    >
                      No, keep them separate
                    </Button>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:grid-cols-3">
                <div>
                  <Label htmlFor="personalAddress">
                    Personal Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="personalAddress"
                    {...form.register("personalAddress")}
                    placeholder="123 Home Street"
                    className={errors.personalAddress ? "border-red-500" : ""}
                    disabled={watchedValues.sameAddress}
                  />
                  {errors.personalAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.personalAddress.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="personalSuiteUnit">Personal Suite/Unit</Label>
                  <Input
                    id="personalSuiteUnit"
                    {...form.register("personalSuiteUnit")}
                    placeholder="Suite 100"
                    disabled={watchedValues.sameAddress}
                  />
                </div>

                <div>
                  <Label htmlFor="personalCity">
                    Personal City <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="personalCity"
                    {...form.register("personalCity")}
                    placeholder="Calgary"
                    className={errors.personalCity ? "border-red-500" : ""}
                    disabled={watchedValues.sameAddress}
                  />
                  {errors.personalCity && (
                    <p className="text-red-500 text-sm mt-1">{errors.personalCity.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="personalProvince">
                    Personal Province <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={watchedValues.personalProvince}
                    onValueChange={(value) => setValue('personalProvince', value)}
                    disabled={watchedValues.sameAddress}
                  >
                    <SelectTrigger className={errors.personalProvince ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select province" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Alberta">Alberta</SelectItem>
                      <SelectItem value="British Columbia">British Columbia</SelectItem>
                      <SelectItem value="Manitoba">Manitoba</SelectItem>
                      <SelectItem value="New Brunswick">New Brunswick</SelectItem>
                      <SelectItem value="Newfoundland And Labrador">Newfoundland and Labrador</SelectItem>
                      <SelectItem value="Nova Scotia">Nova Scotia</SelectItem>
                      <SelectItem value="Ontario">Ontario</SelectItem>
                      <SelectItem value="Prince Edward Island">Prince Edward Island</SelectItem>
                      <SelectItem value="Quebec">Quebec</SelectItem>
                      <SelectItem value="Saskatchewan">Saskatchewan</SelectItem>
                      <SelectItem value="Northwest Territories">Northwest Territories</SelectItem>
                      <SelectItem value="Nunavut">Nunavut</SelectItem>
                      <SelectItem value="Yukon">Yukon</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.personalProvince && (
                    <p className="text-red-500 text-sm mt-1">{errors.personalProvince.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="personalPostalCode">
                    Personal Postal Code <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="personalPostalCode"
                    {...form.register("personalPostalCode")}
                    placeholder="T2P 1J9"
                    className={errors.personalPostalCode ? "border-red-500" : ""}
                    disabled={watchedValues.sameAddress}
                  />
                  {errors.personalPostalCode && (
                    <p className="text-red-500 text-sm mt-1">{errors.personalPostalCode.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Information Section */}
            <div>
              <FormSectionTitle
                title="Contact Information"
                icon={<Phone className="h-5 w-5" />}
                description="Your contact details"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="workEmail">
                    Preferred Email Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="workEmail"
                    type="email"
                    {...form.register("workEmail")}
                    placeholder="<EMAIL>"
                    className={errors.workEmail ? "border-red-500" : ""}
                  />
                  {errors.workEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.workEmail.message}</p>
                  )}
                </div>

                <div className="flex gap-2">
                  <div className="flex-1">
                    <Label htmlFor="workPhone">
                      Preferred Phone Number <span className="text-red-500">*</span>
                    </Label>
                    <PhoneInput
                      id="workPhone"
                      value={watchedValues.workPhone}
                      onChange={(e) => setValue('workPhone', e.target.value)}
                      className={errors.workPhone ? "border-red-500" : ""}
                    />
                    {errors.workPhone && (
                      <p className="text-red-500 text-sm mt-1">{errors.workPhone.message}</p>
                    )}
                  </div>
                  <div className="w-20">
                    <Label htmlFor="ext">Ext.</Label>
                    <Input
                      id="ext"
                      {...form.register("ext")}
                      placeholder="123"
                    />
                  </div>
                </div>              
              
                <div>
                  <Label htmlFor="homePhone">Home Phone</Label>
                  <PhoneInput
                    id="homePhone"
                    value={watchedValues.homePhone}
                    onChange={(e) => setValue('homePhone', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="cellPhone">Cell Phone</Label>
                  <PhoneInput
                    id="cellPhone"
                    value={watchedValues.cellPhone}
                    onChange={(e) => setValue('cellPhone', e.target.value)}
                  />
                </div>

                

                <div>
                  <Label htmlFor="emergencyContact">
                    Emergency Contact Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="emergencyContact"
                    {...form.register("emergencyContact")}
                    placeholder="Emergency contact name"
                    className={errors.emergencyContact ? "border-red-500" : ""}
                  />
                  {errors.emergencyContact && (
                    <p className="text-red-500 text-sm mt-1">{errors.emergencyContact.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="emergencyPhone">
                    Emergency Contact Phone <span className="text-red-500">*</span>
                  </Label>
                  <PhoneInput
                    id="emergencyPhone"
                    value={watchedValues.emergencyPhone}
                    onChange={(e) => setValue('emergencyPhone', e.target.value)}
                    className={errors.emergencyPhone ? "border-red-500" : ""}
                  />
                  {errors.emergencyPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.emergencyPhone.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Social Media Section */}
            <div>
              <FormSectionTitle
                title="Social Media"
                description="Are you marketing mortgages in social media? Please add below the links and handles of your pages:"
              />

              <div className="space-y-6">
                {/* Facebook */}
                <div>
                  <SwitcherBox
                    id="hasFacebook"
                    name="hasFacebook"
                    checked={watchedValues.hasFacebook}
                    onChange={(checked) => handleSocialMediaToggle('Facebook', checked)}
                    label="Facebook"
                    yesno={true}
                  />
                  {watchedValues.hasFacebook && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="facebook">Facebook Link</Label>
                        <Input
                          id="facebook"
                          value={watchedValues.facebook}
                          onChange={(e) => handleSocialMediaUrlChange('facebook', e.target.value)}
                          placeholder="https://facebook.com/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="facebookHandler">Facebook Handler</Label>
                        <Input
                          id="facebookHandler"
                          {...form.register("facebookHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Instagram */}
                <div>
                  <SwitcherBox
                    id="hasInstagram"
                    name="hasInstagram"
                    checked={watchedValues.hasInstagram}
                    onChange={(checked) => handleSocialMediaToggle('Instagram', checked)}
                    label="Instagram"
                    yesno={true}
                  />
                  {watchedValues.hasInstagram && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="instagram">Instagram Link</Label>
                        <Input
                          id="instagram"
                          value={watchedValues.instagram}
                          onChange={(e) => handleSocialMediaUrlChange('instagram', e.target.value)}
                          placeholder="https://instagram.com/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="instagramHandler">Instagram Handler</Label>
                        <Input
                          id="instagramHandler"
                          {...form.register("instagramHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* LinkedIn */}
                <div>
                  <SwitcherBox
                    id="hasLinkedin"
                    name="hasLinkedin"
                    checked={watchedValues.hasLinkedin}
                    onChange={(checked) => handleSocialMediaToggle('Linkedin', checked)}
                    label="LinkedIn"
                    yesno={true}
                  />
                  {watchedValues.hasLinkedin && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="linkedin">LinkedIn Link</Label>
                        <Input
                          id="linkedin"
                          value={watchedValues.linkedin}
                          onChange={(e) => handleSocialMediaUrlChange('linkedin', e.target.value)}
                          placeholder="https://linkedin.com/in/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="linkedinHandler">LinkedIn Handler</Label>
                        <Input
                          id="linkedinHandler"
                          {...form.register("linkedinHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* YouTube */}
                <div>
                  <SwitcherBox
                    id="hasYoutube"
                    name="hasYoutube"
                    checked={watchedValues.hasYoutube}
                    onChange={(checked) => handleSocialMediaToggle('Youtube', checked)}
                    label="YouTube"
                    yesno={true}
                  />
                  {watchedValues.hasYoutube && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="youtube">YouTube Link</Label>
                        <Input
                          id="youtube"
                          value={watchedValues.youtube}
                          onChange={(e) => handleSocialMediaUrlChange('youtube', e.target.value)}
                          placeholder="https://youtube.com/c/yourchannel"
                        />
                      </div>
                      <div>
                        <Label htmlFor="youtubeHandler">YouTube Handler</Label>
                        <Input
                          id="youtubeHandler"
                          {...form.register("youtubeHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Twitter/X */}
                <div>
                  <SwitcherBox
                    id="hasTwitter"
                    name="hasTwitter"
                    checked={watchedValues.hasTwitter}
                    onChange={(checked) => handleSocialMediaToggle('Twitter', checked)}
                    label="Twitter/X"
                    yesno={true}
                  />
                  {watchedValues.hasTwitter && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="twitter">Twitter/X Link</Label>
                        <Input
                          id="twitter"
                          value={watchedValues.twitter}
                          onChange={(e) => handleSocialMediaUrlChange('twitter', e.target.value)}
                          placeholder="https://twitter.com/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="twitterHandler">Twitter/X Handler</Label>
                        <Input
                          id="twitterHandler"
                          {...form.register("twitterHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* TikTok */}
                <div>
                  <SwitcherBox
                    id="hasTikTok"
                    name="hasTikTok"
                    checked={watchedValues.hasTikTok}
                    onChange={(checked) => handleSocialMediaToggle('TikTok', checked)}
                    label="TikTok"
                    yesno={true}
                  />
                  {watchedValues.hasTikTok && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="tiktok">TikTok Link</Label>
                        <Input
                          id="tiktok"
                          value={watchedValues.tiktok}
                          onChange={(e) => handleSocialMediaUrlChange('tiktok', e.target.value)}
                          placeholder="https://tiktok.com/@yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="tiktokHandler">TikTok Handler</Label>
                        <Input
                          id="tiktokHandler"
                          {...form.register("tiktokHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Pinterest */}
                <div>
                  <SwitcherBox
                    id="hasPinterest"
                    name="hasPinterest"
                    checked={watchedValues.hasPinterest}
                    onChange={(checked) => handleSocialMediaToggle('Pinterest', checked)}
                    label="Pinterest"
                    yesno={true}
                  />
                  {watchedValues.hasPinterest && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="pinterest">Pinterest Link</Label>
                        <Input
                          id="pinterest"
                          value={watchedValues.pinterest}
                          onChange={(e) => handleSocialMediaUrlChange('pinterest', e.target.value)}
                          placeholder="https://pinterest.com/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="pinterestHandler">Pinterest Handler</Label>
                        <Input
                          id="pinterestHandler"
                          {...form.register("pinterestHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Threads */}
                <div>
                  <SwitcherBox
                    id="hasThreads"
                    name="hasThreads"
                    checked={watchedValues.hasThreads}
                    onChange={(checked) => handleSocialMediaToggle('Threads', checked)}
                    label="Threads"
                    yesno={true}
                  />
                  {watchedValues.hasThreads && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="threads">Threads Link</Label>
                        <Input
                          id="threads"
                          value={watchedValues.threads}
                          onChange={(e) => handleSocialMediaUrlChange('threads', e.target.value)}
                          placeholder="https://threads.net/@yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="threadsHandler">Threads Handler</Label>
                        <Input
                          id="threadsHandler"
                          {...form.register("threadsHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Bluesky */}
                <div>
                  <SwitcherBox
                    id="hasBluesky"
                    name="hasBluesky"
                    checked={watchedValues.hasBluesky}
                    onChange={(checked) => handleSocialMediaToggle('Bluesky', checked)}
                    label="Bluesky"
                    yesno={true}
                  />
                  {watchedValues.hasBluesky && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <Label htmlFor="bluesky">Bluesky Link</Label>
                        <Input
                          id="bluesky"
                          value={watchedValues.bluesky}
                          onChange={(e) => handleSocialMediaUrlChange('bluesky', e.target.value)}
                          placeholder="https://bsky.app/profile/yourprofile"
                        />
                      </div>
                      <div>
                        <Label htmlFor="blueskyHandler">Bluesky Handler</Label>
                        <Input
                          id="blueskyHandler"
                          {...form.register("blueskyHandler")}
                          placeholder="@yourhandle"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Declaration Section - Only for licensed brokers */}
            {isLicensed && (
              <div>
                <FormSectionTitle
                  title="Declarations"
                  icon={<Scale className="h-5 w-5" />}
                  description="Please answer the following declarations"
                />

                <div className="space-y-6">
                  <div>
                    <Label className="text-base font-medium">
                      Have you ever been subject to regulatory review or disciplinary action?
                    </Label>
                    <div className="mt-2">
                      <SwitcherBox
                        id="declarationRegulatoryReview"
                        name="declarationRegulatoryReview"
                        checked={watchedValues.declarationRegulatoryReview === true}
                        onChange={(checked) => setValue('declarationRegulatoryReview', checked)}
                        yesno={true}
                      />
                    </div>
                    {watchedValues.declarationRegulatoryReview === true && (
                      <div className="mt-4">
                        <Label htmlFor="declarationRegulatoryReviewDetails">
                          Please provide details <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                          id="declarationRegulatoryReviewDetails"
                          {...form.register("declarationRegulatoryReviewDetails")}
                          placeholder="Please provide details..."
                          className="min-h-[80px]"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label className="text-base font-medium">
                      Have you ever had client complaints filed against you?
                    </Label>
                    <div className="mt-2">
                      <SwitcherBox
                        id="declarationClientComplaints"
                        name="declarationClientComplaints"
                        checked={watchedValues.declarationClientComplaints === true}
                        onChange={(checked) => setValue('declarationClientComplaints', checked)}
                        yesno={true}
                      />
                    </div>
                    {watchedValues.declarationClientComplaints === true && (
                      <div className="mt-4">
                        <Label htmlFor="declarationClientComplaintsDetails">
                          Please provide details <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                          id="declarationClientComplaintsDetails"
                          {...form.register("declarationClientComplaintsDetails")}
                          placeholder="Please provide details..."
                          className="min-h-[80px]"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Signature Section */}
            <div>
              <FormSectionTitle
                title="Signature"
                icon={<FileText className="h-5 w-5" />}
                description="Draw your signature on the rectangle below and save it."
              />

              <SignatureSection
                value={watchedValues.signature}
                onSignatureChange={(signature) => setValue('signature', signature || '')}
                setValue={setValue}
                fieldName="signature"
                formName="brokerInfo"
                label="Signature"
                required={true}
                allowOneClick={true}
                showProfileSignature={true}
                showQualityFeedback={true}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => validateForm(watchedValues)}
              >
                Validate Form
              </Button>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Information"}
              </Button>
            </div>

            {/* Navigation Footer */}
            <NextPrevFooter />
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
