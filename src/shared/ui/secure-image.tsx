"use client";

import * as React from "react";
import { api } from "@/shared/lib/api";
import { cn } from "@/shared/lib/utils";

interface SecureImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
  fallbackText?: string;
  showLoading?: boolean;
}

/**
 * SecureImage component that handles both public and private S3 URLs
 * Replicates the legacy SecureImage.js functionality with modern React patterns
 * 
 * Features:
 * - JWT token authentication for private images
 * - Multiple token source fallbacks (cookies, localStorage)
 * - Binary response handling with blob URL creation
 * - Error handling and fallback mechanisms
 * - Loading states and error states
 */
export function SecureImage({
  src,
  alt = "",
  className,
  style,
  fallbackText = "Image unavailable",
  showLoading = true,
  ...props
}: SecureImageProps) {
  const [imageSrc, setImageSrc] = React.useState<string>("");
  const [error, setError] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [errorDetails, setErrorDetails] = React.useState<string>("");

  // Helper function to get JWT token from various sources
  const getJwtToken = React.useCallback((): string | null => {
    // First try from document.cookie
    const tokenFromCookie = document.cookie
      .split('; ')
      .find((row) => row.startsWith('jwt='))
      ?.split('=')[1];

    if (tokenFromCookie) {
      return tokenFromCookie;
    }

    // If not in cookies, try localStorage as fallback
    try {
      return localStorage.getItem('jwt');
    } catch (e) {
      console.error('Error accessing localStorage:', e);
      return null;
    }
  }, []);

  // Helper function to get accessible URL for S3 proxy
  const getAccessibleUrl = React.useCallback((originalUrl: string): string => {
    if (!originalUrl) return '';
    
    // If it's already a proxy URL, return as is
    if (originalUrl.includes('/api/s3proxy')) {
      return originalUrl;
    }
    
    // Convert S3 URL to proxy URL
    const encodedUrl = encodeURIComponent(originalUrl);
    return `/api/s3proxy?url=${encodedUrl}`;
  }, []);

  React.useEffect(() => {
    console.log('SecureImage received src:', src);

    if (!src) {
      console.log('SecureImage: src is empty');
      setError(true);
      setErrorDetails('No image URL provided');
      setLoading(false);
      return;
    }

    const fetchImage = async () => {
      try {
        // Check if this is a data URL (base64 image)
        const isDataUrl = src.startsWith('data:');
        
        // Check if this is an S3 URL
        const isS3Url = src.includes('amazonaws.com');
        const isPrivateS3 = isS3Url && src.includes('/private/');

        // For data URLs, use them directly (no proxy needed)
        if (isDataUrl) {
          console.log('Using data URL directly:', src.substring(0, 50) + '...');
          setImageSrc(src);
          setLoading(false);
          setError(false);
          return;
        }

        // For public S3 URLs, use them directly
        if (isS3Url && !isPrivateS3) {
          console.log('Using public S3 URL directly:', src);
          setImageSrc(src);
          setLoading(false);
          setError(false);
          return;
        }

        // For private S3 URLs or other URLs, use the proxy
        const accessibleUrl = getAccessibleUrl(src);
        console.log('Fetching image from:', accessibleUrl);

        // Get the JWT token
        const token = getJwtToken();

        if (!token && isPrivateS3) {
          console.error('No JWT token found for authentication of private image');
        }

        // Make request to proxy with authentication
        const response = await api.get(accessibleUrl, {
          headers: {
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          responseType: 'blob', // Important for binary image data
        });

        console.log('Proxy response received with status:', response.status);

        // Handle binary image data
        if (response.success && response.data) {
          const contentType = response.headers?.['content-type'] || 'image/png';
          const blob = new Blob([response.data], { type: contentType });
          const blobUrl = URL.createObjectURL(blob);

          console.log('Created blob URL from image data:', blobUrl);
          setImageSrc(blobUrl);
          setLoading(false);
          setError(false);
        } else {
          console.error('Unexpected response status:', response.status);
          setError(true);
          setErrorDetails(`Server returned status: ${response.status}`);
          setLoading(false);
        }
      } catch (err) {
        console.error('Error fetching image:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';

        // If there was an error, try using the original S3 URL as fallback
        if (src.includes('amazonaws.com')) {
          console.log('Trying original S3 URL as fallback');
          setImageSrc(src);
          setLoading(false);
        } else {
          setError(true);
          setErrorDetails(`Failed to load image: ${errorMessage}`);
          setLoading(false);
        }
      }
    };

    fetchImage();

    // Clean up blob URL on unmount
    return () => {
      if (imageSrc && imageSrc.startsWith('blob:')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src, getJwtToken, getAccessibleUrl]);

  // Handle image load error
  const handleImageError = React.useCallback(() => {
    console.error('Error loading image with URL:', imageSrc);

    // If the image fails to load and we're not already using the original URL,
    // try the original URL as a last resort
    if (imageSrc !== src) {
      console.log('Trying original URL as last resort:', src);
      setImageSrc(src);
    } else {
      setError(true);
    }
  }, [imageSrc, src]);

  if (error) {
    return (
      <div
        className={cn("bg-red-50 border border-red-200 p-3 text-center", className)}
        style={style}
      >
        <div className="text-red-600 text-sm">
          <p className="font-medium">{fallbackText}</p>
          {errorDetails && (
            <p className="text-xs mt-1 text-red-500">{errorDetails}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      {loading && showLoading && (
        <div className="p-2 text-center text-gray-500">Loading image...</div>
      )}
      {!loading && !error && (
        <img
          src={imageSrc}
          alt={alt}
          className={className}
          style={style}
          onError={handleImageError}
          {...props}
        />
      )}
    </>
  );
}
