// helpers/signatureHelper.ts

import SignatureCanvas from "react-signature-canvas";
import * as htmlToImage from "html-to-image";

// Types for signature quality and ink detection
export type SignatureQuality = 'poor' | 'good' | 'excellent';

export interface InkDetectionResult {
  isInked: boolean;
  inkAmount: number;
  quality: SignatureQuality;
}

export interface SignatureValidationResult {
  isValid: boolean;
  hasInk: boolean;
  hasPixels: boolean;
  quality: SignatureQuality;
  errorMessage?: string;
}

/**
 * Captures signature from canvas and converts it to a blob
 * Works across different browsers including Safari
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param containerRef - Reference to the container div
 * @returns Promise that resolves to the signature blob
 */
export const captureSignature = async (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  containerRef: React.RefObject<HTMLDivElement>
): Promise<Blob> => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    throw new Error('Signature is empty');
  }

  try {
    let signatureBlob: Blob;

    // Safari-specific handling
    if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
      // Get the canvas element and its data directly
      const canvas = signaturePadRef.current.getCanvas();
      const dataUrl = canvas.toDataURL('image/png');

      // Convert base64 to blob
      const base64Response = await fetch(dataUrl);
      signatureBlob = await base64Response.blob();
    } else {
      // For other browsers, use html-to-image
      if (!containerRef.current) {
        throw new Error('Container reference not found');
      }
      const blob = await htmlToImage.toBlob(containerRef.current);
      if (!blob) {
        throw new Error('Failed to generate signature blob');
      }
      signatureBlob = blob;
    }

    // Validate blob
    if (!signatureBlob || signatureBlob.size < 1000) {
      throw new Error('Generated signature appears to be empty or invalid');
    }

    return signatureBlob;
  } catch (error) {
    throw new Error(`Failed to capture signature: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Creates a FormData object with the signature blob and metadata
 *
 * @param signatureBlob - The signature blob
 * @param user - User object containing firstname and lastname
 * @param fieldName - Name of the field in the backend (e.g., 'signature')
 * @returns FormData object ready for upload
 */
export const createSignatureFormData = (
  signatureBlob: Blob,
  user: { firstname?: string; lastName?: string; id: string },
  fieldName = 'signature'
): FormData => {
  const formData = new FormData();
  const firstName = user.firstname || 'unknown';
  const lastName = user.lastName || 'unknown';
  
  formData.append('files', signatureBlob, `applicantSignature-${firstName}-${lastName}.png`);
  formData.append('refId', user.id);
  formData.append('source', 'users-permissions');
  formData.append('ref', 'plugin::users-permissions.user'); // Strapi V5 format for users-permissions plugin
  formData.append('field', fieldName);
  return formData;
};

/**
 * Legacy ink detection function that replicates the original handleSignature logic
 * Uses the signature pad's internal _data to count ink strokes
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param minInkThreshold - Minimum number of data points required (default: 30)
 * @returns InkDetectionResult with ink status and quality assessment
 */
export const detectInkAmount = (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  minInkThreshold = 30
): InkDetectionResult => {
  if (!signaturePadRef.current) {
    return { isInked: false, inkAmount: 0, quality: 'poor' };
  }

  try {
    // Access the signature pad's internal data (legacy method)
    const signaturePad = (signaturePadRef.current as any).getSignaturePad();
    if (!signaturePad || !signaturePad._data) {
      return { isInked: false, inkAmount: 0, quality: 'poor' };
    }

    // Flatten the data array to count total ink points
    const inkAmountArr = signaturePad._data;
    const inkAmount = inkAmountArr.reduce(
      (accumulator: any[], currentValue: any[]) => accumulator.concat(currentValue),
      []
    );

    const totalInkPoints = inkAmount.length;
    const isInked = totalInkPoints >= minInkThreshold;

    // Determine quality based on ink amount
    let quality: SignatureQuality = 'poor';
    if (totalInkPoints >= 100) {
      quality = 'excellent';
    } else if (totalInkPoints >= 60) {
      quality = 'good';
    }

    return {
      isInked,
      inkAmount: totalInkPoints,
      quality
    };
  } catch (error) {
    console.error('Error detecting ink amount:', error);
    return { isInked: false, inkAmount: 0, quality: 'poor' };
  }
};

/**
 * Comprehensive signature validation that combines both pixel counting and ink detection
 * Provides detailed validation results for better error handling
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param minInkThreshold - Minimum number of ink data points required (default: 30)
 * @returns SignatureValidationResult with detailed validation information
 */
export const validateSignatureQuality = (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  minInkThreshold = 30
): SignatureValidationResult => {
  if (!signaturePadRef.current) {
    return {
      isValid: false,
      hasInk: false,
      hasPixels: false,
      quality: 'poor',
      errorMessage: 'Signature component not initialized'
    };
  }

  if (signaturePadRef.current.isEmpty()) {
    return {
      isValid: false,
      hasInk: false,
      hasPixels: false,
      quality: 'poor',
      errorMessage: 'Signature is empty'
    };
  }

  // Check ink detection
  const inkResult = detectInkAmount(signaturePadRef, minInkThreshold);

  // Check pixel validation
  const hasPixels = hasValidSignature(signaturePadRef);

  // Determine overall validity
  const isValid = inkResult.isInked && hasPixels;

  let errorMessage: string | undefined;
  if (!inkResult.isInked) {
    errorMessage = 'Please add a real signature';
  } else if (!hasPixels) {
    errorMessage = 'Signature appears to be invalid';
  }

  return {
    isValid,
    hasInk: inkResult.isInked,
    hasPixels,
    quality: inkResult.quality,
    errorMessage
  };
};

/**
 * Checks if a signature canvas has been signed by the user
 * This is more reliable than just checking isEmpty() as it validates actual content
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @returns boolean indicating if signature has meaningful content
 */
export const hasValidSignature = (signaturePadRef: React.RefObject<SignatureCanvas>): boolean => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    return false;
  }

  try {
    // Get the canvas and its image data
    const canvas = signaturePadRef.current.getCanvas();
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      return false;
    }

    // Get image data from the canvas
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Count non-white pixels (assuming white background)
    let nonWhitePixels = 0;
    for (let i = 0; i < data.length; i += 4) {
      // Check if pixel is not white (RGB values not all 255)
      if (data[i] !== 255 || data[i + 1] !== 255 || data[i + 2] !== 255) {
        nonWhitePixels++;
      }
    }
    
    // Consider signature valid if there are enough non-white pixels
    // This threshold can be adjusted based on requirements
    return nonWhitePixels > 100;
  } catch (error) {
    console.error('Error validating signature:', error);
    return false;
  }
};

/**
 * Clears the signature canvas and resets its state
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 */
export const clearSignature = (signaturePadRef: React.RefObject<SignatureCanvas>): void => {
  if (signaturePadRef.current) {
    signaturePadRef.current.clear();
  }
};

/**
 * Loads a signature from a data URL into the canvas
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param dataUrl - The data URL of the signature to load
 */
export const loadSignature = (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  dataUrl: string
): void => {
  if (signaturePadRef.current && dataUrl) {
    signaturePadRef.current.fromDataURL(dataUrl);
  }
};

/**
 * Gets the signature as a data URL
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @returns The signature as a data URL or null if empty
 */
export const getSignatureDataURL = (signaturePadRef: React.RefObject<SignatureCanvas>): string | null => {
  if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
    return null;
  }
  
  return signaturePadRef.current.toDataURL();
};

/**
 * Saves signature to the backend and updates user data
 * Replicates the legacy signature saving logic
 *
 * @param signatureDataUrl - The signature as a data URL
 * @param user - User object
 * @param api - API client instance
 * @param fieldName - Field name for the signature (default: 'signature')
 * @returns Promise that resolves to the uploaded signature data
 */
export const saveSignatureToBackend = async (
  signatureDataUrl: string,
  user: { firstname?: string; lastname?: string; id: string },
  api: any,
  fieldName = 'signature'
): Promise<any> => {
  try {
    // Convert data URL to blob
    const response = await fetch(signatureDataUrl);
    const signatureBlob = await response.blob();
    
    // Create form data for upload
    const formData = createSignatureFormData(signatureBlob, user, fieldName);
    
    // Debug: Log the form data contents
    console.log('Upload form data contents:');
    for (const [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }
    
    // Upload signature to backend
    const uploadResponse = await api.post('/upload', formData);
    
    if (!uploadResponse.success || !uploadResponse.data) {
      console.error('Upload failed:', uploadResponse);
      throw new Error('Failed to upload signature');
    }
    
    const signatureData = uploadResponse.data[0];
    console.log('Upload successful:', signatureData);
    
    // Update user with signature data
    const userUpdateResponse = await api.put(`/users/${user.id}`, {
      [fieldName]: signatureData
    });
    
    if (!userUpdateResponse.success) {
      console.error('User update failed:', userUpdateResponse);
      throw new Error('Failed to update user signature');
    }
    
    console.log('User update successful:', userUpdateResponse.data);
    return signatureData;
  } catch (error) {
    console.error('Error saving signature:', error);
    throw new Error(`Failed to save signature: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Checks if a signature exists and is saved
 * Replicates the legacy checkSignature function
 *
 * @param signature - Signature object from form data
 * @returns Object with isSaved status and signature data
 */
export const checkSignature = (signature: any): { isSaved: boolean; data?: any } => {
  if (signature && signature !== null && signature.url && signature.url.length > 0) {
    return { isSaved: true, data: signature };
  }
  return { isSaved: false };
};

/**
 * Checks if there are initials and returns status
 * Replicates the legacy checkInitials function for contract forms
 *
 * @param initials - Initials object with url property
 * @returns Object with isSaved status and data
 */
export const checkInitials = (initials?: { url?: string; [key: string]: any }): { isSaved: boolean; data?: any } => {
  if (initials && initials.url && initials.url.length > 0) {
    return { isSaved: true, data: initials };
  }
  return { isSaved: false };
};

/**
 * Legacy-compatible signature validation using ink detection
 * Replicates the original hasValidSignatureLegacy behavior
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @param minInkThreshold - Minimum number of ink data points required (default: 30)
 * @returns boolean indicating if signature has sufficient ink
 */
export const hasValidSignatureLegacy = (
  signaturePadRef: React.RefObject<SignatureCanvas>,
  minInkThreshold = 30
): boolean => {
  const inkResult = detectInkAmount(signaturePadRef, minInkThreshold);
  return inkResult.isInked;
};

/**
 * Gets the raw ink amount from signature pad
 * Useful for debugging and quality assessment
 *
 * @param signaturePadRef - Reference to the SignaturePad component
 * @returns number of ink data points
 */
export const getInkAmount = (signaturePadRef: React.RefObject<SignatureCanvas>): number => {
  const inkResult = detectInkAmount(signaturePadRef);
  return inkResult.inkAmount;
};
