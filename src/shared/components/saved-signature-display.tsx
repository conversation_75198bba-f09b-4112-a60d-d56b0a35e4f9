"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { SecureImage } from "@/shared/ui/secure-image";
import { CheckCircle } from "lucide-react";
import { cn } from "@/shared/lib/utils";

interface SavedSignatureDisplayProps {
  signatureUrl: string;
  signatureName?: string;
  signatureDate?: string;
  onResign?: () => void;
  showResignButton?: boolean;
  className?: string;
}

/**
 * SavedSignatureDisplay component that shows a saved signature
 * with metadata and optional re-sign functionality
 */
export function SavedSignatureDisplay({
  signatureUrl,
  signatureName,
  signatureDate,
  onResign,
  showResignButton = true,
  className
}: SavedSignatureDisplayProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2">
        <CheckCircle className="h-5 w-5 text-green-600" />
        <h3 className="text-lg font-semibold text-green-600">Signature Saved!</h3>
      </div>
      
      {/* Signature Display */}
      <div className="border border-gray-300 bg-white p-2 inline-block">
        <SecureImage
          src={signatureUrl}
          alt={signatureName || 'Saved Signature'}
          className="w-full h-auto"
          style={{ maxWidth: '400px', height: 'auto' }}
        />
      </div>
      
      {/* Signature Metadata */}
      <div className="text-sm text-muted-foreground space-y-1">
        {signatureName && (
          <p>Signature: {signatureName}</p>
        )}
        {signatureDate && (
          <p>Signed on: {new Date(signatureDate).toLocaleDateString()}</p>
        )}
      </div>
      
      {/* Re-sign Button */}
      {showResignButton && onResign && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onResign}
          className="mt-2"
        >
          Re-sign
        </Button>
      )}
    </div>
  );
}
