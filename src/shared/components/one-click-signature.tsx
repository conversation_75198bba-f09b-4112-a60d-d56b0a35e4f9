"use client";

import * as React from "react";
import { But<PERSON> } from "@/shared/ui/button";
import { SecureImage } from "@/shared/ui/secure-image";
import { cn } from "@/shared/lib/utils";

interface OneClickSignatureProps {
  signatureUrl: string;
  signatureName?: string;
  onOneClickSign: () => Promise<void>;
  disabled?: boolean;
  className?: string;
}

/**
 * OneClickSignature component that displays an existing signature
 * and allows users to reuse it with a single click
 * 
 * Replicates the legacy one-click signing functionality
 */
export function OneClickSignature({
  signatureUrl,
  signatureName,
  onOneClickSign,
  disabled = false,
  className
}: OneClickSignatureProps) {
  const [isSigning, setIsSigning] = React.useState(false);

  const handleOneClickSign = async () => {
    setIsSigning(true);
    try {
      await onOneClickSign();
    } catch (error) {
      console.error('Error with one-click signing:', error);
    } finally {
      setIsSigning(false);
    }
  };

  return (
    <div className={cn("space-y-4", className)} style={{ margin: '54px 0' }}>
      <h2 className="text-lg font-semibold">One click sign</h2>
      
      {/* Signature Preview */}
      <div 
        className="border border-gray-300 bg-white p-2 inline-block"
        style={{ width: '400px', display: 'block' }}
      >
        <SecureImage
          src={signatureUrl}
          alt={signatureName || 'Signature'}
          className="w-full h-auto"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
      </div>
      
      <p className="text-sm text-muted-foreground">
        Click on the button below to use your signature added previously.
      </p>
      
      <Button
        type="button"
        variant="default"
        size="default"
        onClick={handleOneClickSign}
        disabled={disabled || isSigning}
        className="bg-blue-600 hover:bg-blue-700 text-white"
      >
        {isSigning ? "Signing..." : "Click to Sign and Save"}
      </Button>
    </div>
  );
}
