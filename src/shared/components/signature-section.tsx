"use client";

import * as React from "react";
import { SignatureCapture } from "@/shared/ui/signature-capture";
import { OneClickSignature } from "@/shared/components/one-click-signature";
import { SavedSignatureDisplay } from "@/shared/components/saved-signature-display";
import { saveSignatureToBackend, checkSignature } from "@/shared/lib/signature-helper";
import { api } from "@/shared/lib/api";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { useFormsContext } from "@/shared/contexts/forms-context";
import { toast } from "@/shared/lib/toast";
import type { OnboardingForms } from "@/shared/types/forms";

interface SignatureSectionProps {
  value?: string;
  onSignatureChange: (signature: string | null) => void;
  setValue: (field: any, value: any) => void;
  fieldName?: string;
  formName?: keyof OnboardingForms; // Add formName prop for saving to form data
  label?: string;
  required?: boolean;
  className?: string;
  allowOneClick?: boolean;
  showProfileSignature?: boolean;
  showQualityFeedback?: boolean;
  minInkThreshold?: number;
}

export function SignatureSection({
  value,
  onSignatureChange,
  setValue,
  fieldName = 'signature',
  formName,
  label = "Signature",
  required = true,
  className,
  allowOneClick = true,
  showProfileSignature = true,
  showQualityFeedback = false,
  minInkThreshold = 30
}: SignatureSectionProps) {
  const { userAuth } = useAuthContext();
  const { updateForm, saveForm } = useFormsContext();
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });
  const [isSavingSignature, setIsSavingSignature] = React.useState(false);
  const [showCanvas, setShowCanvas] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState<string>('');
  const [showError, setShowError] = React.useState(false);

  // Handle signature saving
  const handleSignatureSave = async (signatureDataUrl: string) => {
    setIsSavingSignature(true);
    try {
      const user = {
        firstname: userAuth?.userInfo?.firstname || '',
        lastname: userAuth?.userInfo?.lastname || '',
        id: userAuth?.userInfo?.id || ''
      };

      // Save signature to backend (S3 + user profile)
      const signatureData = await saveSignatureToBackend(signatureDataUrl, user, api, fieldName);
      
      // Update local state
      setSignature({ isSaved: true, data: signatureData });
      setValue(fieldName, signatureDataUrl);
      onSignatureChange(signatureDataUrl);
      
      // Also save to form data immediately for persistence
      if (formName) {
        // Only update the signature field, not the entire form
        updateForm(formName, {
          [fieldName]: signatureData.id, // Only send the upload ID, not the full response
          signatureDate: new Date().toISOString(),
        });
        
        // Save form to backend immediately
        await saveForm(formName);
      }
      
      toast.success('Signature saved successfully');
      setShowError(false);
      setErrorMessage('');
    } catch (error) {
      console.error('Error saving signature:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to save signature';
      setErrorMessage(errorMsg);
      setShowError(true);
      toast.error(errorMsg);

      // Hide error after 10 seconds
      setTimeout(() => setShowError(false), 10000);
    } finally {
      setIsSavingSignature(false);
    }
  };

  // Handle one-click signing using existing user signature or initials
  const handleOneClickSign = async () => {
    // Determine if this is for initials or signature
    const isInitials = fieldName.toLowerCase().includes('initial');
    const existingData = isInitials
      ? userAuth?.userInfo?.initials
      : userAuth?.userInfo?.signature;

    if (!existingData?.url) {
      toast.error(`No existing ${isInitials ? 'initials' : 'signature'} found`);
      return;
    }

    setIsSavingSignature(true);
    try {
      // Update form state with existing signature/initials
      setSignature({ isSaved: true, data: existingData });
      setValue(fieldName, existingData.url);
      onSignatureChange(existingData.url);

      // Also save to form data immediately for persistence
      if (formName) {
        // Only update the signature field, not the entire form
        updateForm(formName, {
          [fieldName]: existingData.id, // Only send the upload ID, not the full response
          signatureDate: new Date().toISOString(),
        });
        
        // Save form to backend immediately
        await saveForm(formName);
      }

      toast.success(`${isInitials ? 'Initials' : 'Signature'} applied successfully`);
      setShowError(false);
      setErrorMessage('');
    } catch (error) {
      console.error('Error with one-click signing:', error);
      const errorMsg = error instanceof Error ? error.message : `Failed to apply ${isInitials ? 'initials' : 'signature'}`;
      setErrorMessage(errorMsg);
      setShowError(true);
      toast.error(errorMsg);

      // Hide error after 10 seconds
      setTimeout(() => setShowError(false), 10000);
    } finally {
      setIsSavingSignature(false);
    }
  };

  // Handle re-signing (show canvas again)
  const handleResign = () => {
    setShowCanvas(true);
    setSignature({ isSaved: false, data: null });
    onSignatureChange(null);
  };

  // Check if signature exists on component mount
  React.useEffect(() => {
    if (value) {
      const signatureCheck = checkSignature({ url: value });
      setSignature(signatureCheck);
    }
  }, [value]);

  // Determine what to show based on signature status and user profile
  const renderSignatureComponent = () => {
    // If signature is already saved for this form, show saved signature display
    if (signature.isSaved && signature.data?.url && !showCanvas) {
      return (
        <SavedSignatureDisplay
          signatureUrl={signature.data.url}
          signatureName={signature.data.name}
          signatureDate={signature.data.createdAt}
          onResign={handleResign}
          showResignButton={true}
        />
      );
    }

    // If user has a profile signature/initials and one-click is enabled, show one-click option
    const isInitials = fieldName.toLowerCase().includes('initial');
    const existingData = isInitials
      ? userAuth?.userInfo?.initials
      : userAuth?.userInfo?.signature;

    if (
      allowOneClick &&
      showProfileSignature &&
      existingData?.url &&
      !signature.isSaved &&
      !showCanvas
    ) {
      return (
        <OneClickSignature
          signatureUrl={existingData.url}
          signatureName={existingData.name}
          onOneClickSign={handleOneClickSign}
          disabled={isSavingSignature}
        />
      );
    }

    // Default: show signature canvas
    return (
      <SignatureCapture
        value={value}
        onSignatureChange={onSignatureChange}
        onSignatureSave={handleSignatureSave}
        required={required}
        showSaveButton={true}
        label={label}
        showQualityFeedback={showQualityFeedback}
        minInkThreshold={minInkThreshold}
      />
    );
  };

  return (
    <div className={className}>
      {renderSignatureComponent()}

      {/* Error Message Display */}
      {showError && errorMessage && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600 flex items-center gap-2">
            <span className="text-red-500 font-bold">⚠</span>
            {errorMessage}
          </p>
        </div>
      )}

      {/* Signature status message */}
      {!signature.isSaved && !showError && (
        <div className="mt-2">
          <p className="text-sm text-muted-foreground">
            Please save your signature first.
          </p>
        </div>
      )}
    </div>
  );
}

// Export helper to check if signature is saved
export function useSignatureStatus(value?: string) {
  const [signature, setSignature] = React.useState<{ isSaved: boolean; data?: any }>({ isSaved: false, data: null });

  React.useEffect(() => {
    if (value) {
      const signatureCheck = checkSignature({ url: value });
      setSignature(signatureCheck);
    }
  }, [value]);

  return signature;
}
